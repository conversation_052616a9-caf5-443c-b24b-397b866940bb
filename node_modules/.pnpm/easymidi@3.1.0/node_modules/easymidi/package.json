{"name": "<PERSON><PERSON><PERSON>", "version": "3.1.0", "description": "Simple event-based MIDI messaging", "main": "index.js", "scripts": {"test": "mocha tests/test.js"}, "engines": {"node": ">=14.15"}, "repository": {"type": "git", "url": "https://github.com/dinchak/node-easymidi.git"}, "keywords": ["midi"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/dinchak/node-easymidi/issues"}, "homepage": "https://github.com/dinchak/node-easymidi", "devDependencies": {"chai": "^4.3.6", "mocha": "^10.1.0"}, "dependencies": {"@julusian/midi": "^3.0.0-3"}}