{"name": "@julusian/midi", "version": "3.6.1", "scripts": {"install": "pkg-prebuilds-verify ./binding-options.js || node-gyp rebuild", "build": "node-gyp build", "rebuild": "node-gyp clean configure build", "test": "mocha test/unit/*.js && node test/virtual-loopback-test-automated.js"}, "main": "midi.js", "types": "midi.d.ts", "description": "MIDI hardware IO", "author": {"name": "<PERSON>", "email": "******************"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.justinlatimer.com/"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "luc.<PERSON>@freesurf.ch"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}], "license": "MIT", "engines": {"node": ">=14.15"}, "binary": {"napi_versions": [7]}, "dependencies": {"node-addon-api": "^6.1.0", "pkg-prebuilds": "^1.0.0"}, "devDependencies": {"mocha": "^10.5.2", "node-gyp": "^10.1.0", "should": "^13.2.3"}, "repository": {"type": "git", "url": "https://github.com/Julusian/node-midi"}, "files": ["midi.js", "midi.d.ts", "binding.gyp", "binding-options.js", "src/", "lib/", "vendor/rtmidi/RtMidi.cpp", "vendor/rtmidi/RtMidi.h", "prebuilds/"], "packageManager": "yarn@4.3.1"}