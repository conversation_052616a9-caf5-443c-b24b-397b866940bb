{"name": "pkg-prebuilds", "version": "1.0.0", "main": "bindings.js", "license": "MIT", "author": {"name": "<PERSON>", "email": "******************", "url": "https://github.com/julusian"}, "repository": {"type": "git", "url": "git://github.com/julusian/pkg-prebuilds.git"}, "bin": {"pkg-prebuilds-copy": "./bin/copy.mjs", "pkg-prebuilds-verify": "./bin/verify.mjs"}, "engines": {"node": ">= 14.15.0"}, "files": ["bindings.js", "bindings.d.ts", "bin", "lib", "*.md"], "dependencies": {"yargs": "^17.7.2"}}