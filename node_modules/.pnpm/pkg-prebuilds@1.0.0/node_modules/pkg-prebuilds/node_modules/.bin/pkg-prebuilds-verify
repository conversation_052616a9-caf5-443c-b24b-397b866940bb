#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/ExtremeSSD/Dev-Projects/Node.js/MIDI-Clock/node_modules/.pnpm/pkg-prebuilds@1.0.0/node_modules/pkg-prebuilds/bin/node_modules:/mnt/ExtremeSSD/Dev-Projects/Node.js/MIDI-Clock/node_modules/.pnpm/pkg-prebuilds@1.0.0/node_modules/pkg-prebuilds/node_modules:/mnt/ExtremeSSD/Dev-Projects/Node.js/MIDI-Clock/node_modules/.pnpm/pkg-prebuilds@1.0.0/node_modules:/mnt/ExtremeSSD/Dev-Projects/Node.js/MIDI-Clock/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/ExtremeSSD/Dev-Projects/Node.js/MIDI-Clock/node_modules/.pnpm/pkg-prebuilds@1.0.0/node_modules/pkg-prebuilds/bin/node_modules:/mnt/ExtremeSSD/Dev-Projects/Node.js/MIDI-Clock/node_modules/.pnpm/pkg-prebuilds@1.0.0/node_modules/pkg-prebuilds/node_modules:/mnt/ExtremeSSD/Dev-Projects/Node.js/MIDI-Clock/node_modules/.pnpm/pkg-prebuilds@1.0.0/node_modules:/mnt/ExtremeSSD/Dev-Projects/Node.js/MIDI-Clock/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../bin/verify.mjs" "$@"
else
  exec node  "$basedir/../../bin/verify.mjs" "$@"
fi
