hoistPattern:
  - '*'
hoistedDependencies:
  '@julusian/midi@3.6.1':
    '@julusian/midi': private
  ansi-regex@5.0.1:
    ansi-regex: private
  ansi-styles@4.3.0:
    ansi-styles: private
  cliui@8.0.1:
    cliui: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  emoji-regex@8.0.0:
    emoji-regex: private
  escalade@3.2.0:
    escalade: private
  get-caller-file@2.0.5:
    get-caller-file: private
  is-fullwidth-code-point@3.0.0:
    is-fullwidth-code-point: private
  node-addon-api@6.1.0:
    node-addon-api: private
  pkg-prebuilds@1.0.0:
    pkg-prebuilds: private
  require-directory@2.1.1:
    require-directory: private
  string-width@4.2.3:
    string-width: private
  strip-ansi@6.0.1:
    strip-ansi: private
  wrap-ansi@7.0.0:
    wrap-ansi: private
  y18n@5.0.8:
    y18n: private
  yargs-parser@21.1.1:
    yargs-parser: private
  yargs@17.7.2:
    yargs: private
ignoredBuilds: []
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.13.1
pendingBuilds: []
prunedAt: Tue, 22 Jul 2025 23:37:43 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmjs.org/
skipped: []
storeDir: /mnt/ExtremeSSD/.pnpm-store/v10
virtualStoreDir: .pnpm
virtualStoreDirMaxLength: 120
