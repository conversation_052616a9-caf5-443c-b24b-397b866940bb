const easymidi = require('easymidi');
const fs = require('fs');

// font.jsonを読み込み
const fontData = JSON.parse(fs.readFileSync('./font.json', 'utf8'));

// MIDI出力ポートを開く
const outputPortName = 'Midi Through:Midi Through Port-0 14:0';
let output;

try {
    output = new easymidi.Output(outputPortName);
    console.log(`MIDI出力ポート "${outputPortName}" に接続しました`);
} catch (error) {
    console.error(`MIDI出力ポート "${outputPortName}" への接続に失敗しました:`, error.message);
    console.log('\n利用可能なMIDI出力ポート:');
    easymidi.getOutputs().forEach(port => console.log(`  - ${port}`));
    process.exit(1);
}

// 現在時刻を取得してフォーマット（HH:MM:SS）
function getCurrentTimeString() {
    const now = new Date();
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');
    const seconds = String(now.getSeconds()).padStart(2, '0');
    return `${hours}:${minutes}:${seconds}`;
}

// 文字列を6x11のビットマップ配列に変換
function stringToBitmap(timeString) {
    const bitmap = [];
    
    // 各文字のビットマップを横に連結
    for (let row = 0; row < 11; row++) {
        let bitmapRow = [];
        
        for (let charIndex = 0; charIndex < timeString.length; charIndex++) {
            const char = timeString[charIndex];
            const charBitmap = fontData[char];
            
            if (charBitmap) {
                // 6列分のデータを取得（row * 6からrow * 6 + 5まで）
                for (let col = 0; col < 6; col++) {
                    bitmapRow.push(charBitmap[row * 6 + col]);
                }
            } else {
                // 未定義の文字の場合は空白（6列分の0）
                for (let col = 0; col < 6; col++) {
                    bitmapRow.push(0);
                }
            }
        }
        
        bitmap.push(bitmapRow);
    }
    
    return bitmap;
}

// 白鍵のみのノート番号を取得
function getWhiteKeyNote(index) {
    const whiteKeyPattern = [0, 2, 4, 5, 7, 9, 11]; // C, D, E, F, G, A, B
    const octave = Math.floor(index / 7);
    const noteInOctave = index % 7;
    return octave * 12 + whiteKeyPattern[noteInOctave];
}

// ビットマップをMIDIノートに変換して送信
async function sendBitmapAsMidi(bitmap) {
    const velocity = 100;

    // 全てのノートをオフにする
    for (let note = 0; note < 128; note++) {
        try {
            output.send('noteoff', {
                note: note,
                velocity: 0,
                channel: 0
            });
        } catch (error) {
            // エラーを無視
        }
    }

    // 少し待機
    await new Promise(resolve => setTimeout(resolve, 50));

    console.log(`ビットマップサイズ: ${bitmap.length}行 x ${bitmap[0]?.length || 0}列`);

    // ビットマップの構造：11行 x (文字数 * 6)列
    // 各行を異なるMIDIノートにマッピング
    for (let row = 0; row < bitmap.length; row++) {
        for (let col = 0; col < bitmap[row].length; col++) {
            if (bitmap[row][col] === 1) {
                // 行を逆順にして、下の行が低いノート番号になるように
                const invertedRow = bitmap.length - 1 - row;
                // 白鍵のみを使用してノート番号を計算
                const note = getWhiteKeyNote(invertedRow);

                if (note < 128) {
                    console.log(`ノートオン: 行${row}(反転${invertedRow}) 列${col} -> ノート${note} (${getNoteNameFromNumber(note)})`);
                    try {
                        output.send('noteon', {
                            note: note,
                            velocity: velocity,
                            channel: 0
                        });
                    } catch (error) {
                        console.error(`ノート${note}の送信エラー:`, error.message);
                    }
                }
            }
        }
    }
}

// ノート番号から音名を取得（デバッグ用）
function getNoteNameFromNumber(noteNumber) {
    const noteNames = ['C', 'C#', 'D', 'D#', 'E', 'F', 'F#', 'G', 'G#', 'A', 'A#', 'B'];
    const octave = Math.floor(noteNumber / 12);
    const note = noteNames[noteNumber % 12];
    return `${note}${octave}`;
}

// デバッグ用：ビットマップを表示
function printBitmap(bitmap) {
    console.log('ビットマップ:');
    for (let row = 0; row < bitmap.length; row++) {
        let line = '';
        for (let col = 0; col < bitmap[row].length; col++) {
            line += bitmap[row][col] === 1 ? '█' : '·';
        }
        console.log(`${row.toString().padStart(2)}: ${line}`);
    }
    console.log(`サイズ: ${bitmap.length}行 x ${bitmap[0]?.length || 0}列`);
}

// メイン処理
async function updateTimeDisplay() {
    const timeString = getCurrentTimeString();
    console.log(`\n現在時刻: ${timeString}`);

    const bitmap = stringToBitmap(timeString);
    printBitmap(bitmap);
    await sendBitmapAsMidi(bitmap);
}

// MIDIテスト関数
async function testMidi() {
    console.log('MIDIテストを開始...');

    // テスト用のノートを送信
    for (let i = 0; i < 5; i++) {
        const note = getWhiteKeyNote(i);
        console.log(`テストノート${i}: ${note} (${getNoteNameFromNumber(note)})`);

        try {
            output.send('noteon', {
                note: note,
                velocity: 100,
                channel: 0
            });

            await new Promise(resolve => setTimeout(resolve, 200));

            output.send('noteoff', {
                note: note,
                velocity: 0,
                channel: 0
            });

            await new Promise(resolve => setTimeout(resolve, 100));
        } catch (error) {
            console.error(`テストノート${note}のエラー:`, error.message);
        }
    }

    console.log('MIDIテスト完了\n');
}

// 初回にMIDIテストを実行
testMidi().then(() => {
    // テスト後に時刻表示を開始
    updateTimeDisplay();

    // 1秒ごとに更新
    const interval = setInterval(updateTimeDisplay, 1000);

    // intervalをグローバルに保存（終了処理で使用）
    global.displayInterval = interval;
});

// プログラム終了時の処理
process.on('SIGINT', () => {
    console.log('\nプログラムを終了します...');

    if (global.displayInterval) {
        clearInterval(global.displayInterval);
    }

    // 全てのノートをオフにする
    if (output) {
        for (let note = 0; note < 128; note++) {
            try {
                output.send('noteoff', {
                    note: note,
                    velocity: 0,
                    channel: 0
                });
            } catch (error) {
                // エラーを無視
            }
        }
        output.close();
    }

    process.exit(0);
});

console.log('現在時刻をMIDI出力に送信中... (Ctrl+Cで終了)');
