const easymidi = require('easymidi');
const fs = require('fs');

// font.jsonを読み込み
const fontData = JSON.parse(fs.readFileSync('./font.json', 'utf8'));

// MIDI出力ポートを開く
const outputPortName = 'Midi Through:Midi Through Port-0 14:0';
let output;

try {
    output = new easymidi.Output(outputPortName);
    console.log(`MIDI出力ポート "${outputPortName}" に接続しました`);
} catch (error) {
    console.error(`MIDI出力ポート "${outputPortName}" への接続に失敗しました:`, error.message);
    console.log('\n利用可能なMIDI出力ポート:');
    easymidi.getOutputs().forEach(port => console.log(`  - ${port}`));
    process.exit(1);
}

// 現在時刻を取得してフォーマット（HH:MM:SS）
function getCurrentTimeString() {
    const now = new Date();
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');
    const seconds = String(now.getSeconds()).padStart(2, '0');
    return `${hours}:${minutes}:${seconds}`;
}

// 文字列を6x11のビットマップ配列に変換
function stringToBitmap(timeString) {
    const bitmap = [];
    
    // 各文字のビットマップを横に連結
    for (let row = 0; row < 11; row++) {
        let bitmapRow = [];
        
        for (let charIndex = 0; charIndex < timeString.length; charIndex++) {
            const char = timeString[charIndex];
            const charBitmap = fontData[char];
            
            if (charBitmap) {
                // 6列分のデータを取得（row * 6からrow * 6 + 5まで）
                for (let col = 0; col < 6; col++) {
                    bitmapRow.push(charBitmap[row * 6 + col]);
                }
            } else {
                // 未定義の文字の場合は空白（6列分の0）
                for (let col = 0; col < 6; col++) {
                    bitmapRow.push(0);
                }
            }
        }
        
        bitmap.push(bitmapRow);
    }
    
    return bitmap;
}

// 白鍵のみのノート番号を取得
function getWhiteKeyNote(index) {
    const whiteKeyPattern = [0, 2, 4, 5, 7, 9, 11]; // C, D, E, F, G, A, B
    const octave = Math.floor(index / 7);
    const noteInOctave = index % 7;
    return octave * 12 + whiteKeyPattern[noteInOctave];
}

// ビットマップをMIDIノートに変換して送信
async function sendBitmapAsMidi(bitmap) {
    const velocity = 100;

    // 全てのノートをオフにする（0-127の範囲）
    for (let note = 0; note < 128; note++) {
        output.send('noteoff', {
            note: note,
            velocity: 0
        });
    }

    // 少し待機してからノートを送信
    await new Promise(resolve => setTimeout(resolve, 10));

    // ビットマップを線形にスキャンして白鍵にマッピング
    let whiteKeyIndex = 0;
    for (let row = 0; row < bitmap.length; row++) {
        for (let col = 0; col < bitmap[row].length; col++) {
            const note = getWhiteKeyNote(whiteKeyIndex);

            if (note < 128) { // MIDIノート範囲内
                if (bitmap[row][col] === 1) {
                    console.log(`ノートオン: 行${row} 列${col} -> ノート${note}`);
                    output.send('noteon', {
                        note: note,
                        velocity: velocity
                    });
                }
            }
            whiteKeyIndex++;
        }
    }
}

// デバッグ用：ビットマップを表示
function printBitmap(bitmap) {
    console.log('ビットマップ:');
    for (let row = 0; row < bitmap.length; row++) {
        let line = '';
        for (let col = 0; col < bitmap[row].length; col++) {
            line += bitmap[row][col] === 1 ? '█' : '·';
        }
        console.log(`${row.toString().padStart(2)}: ${line}`);
    }
    console.log(`サイズ: ${bitmap.length}行 x ${bitmap[0]?.length || 0}列`);
}

// メイン処理
async function updateTimeDisplay() {
    const timeString = getCurrentTimeString();
    console.log(`\n現在時刻: ${timeString}`);

    const bitmap = stringToBitmap(timeString);
    printBitmap(bitmap);
    await sendBitmapAsMidi(bitmap);
}

// 初回実行
updateTimeDisplay();

// 1秒ごとに更新
const interval = setInterval(updateTimeDisplay, 1000);

// プログラム終了時の処理
process.on('SIGINT', () => {
    console.log('\nプログラムを終了します...');
    clearInterval(interval);
    
    // 全てのノートをオフにする
    if (output) {
        for (let note = 0; note < 128; note++) {
            output.send('noteoff', {
                note: note,
                velocity: 0
            });
        }
        output.close();
    }
    
    process.exit(0);
});

console.log('現在時刻をMIDI出力に送信中... (Ctrl+Cで終了)');
