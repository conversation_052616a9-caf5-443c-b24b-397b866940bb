const easymidi = require('easymidi');
const fs = require('fs');

// font.jsonを読み込み
const fontData = JSON.parse(fs.readFileSync('./font.json', 'utf8'));

// MIDI出力ポートを開く
const outputPortName = 'Midi Through:Midi Through Port-0 14:0';
let output;

try {
    output = new easymidi.Output(outputPortName);
    console.log(`MIDI出力ポート "${outputPortName}" に接続しました`);
} catch (error) {
    console.error(`MIDI出力ポート "${outputPortName}" への接続に失敗しました:`, error.message);
    console.log('\n利用可能なMIDI出力ポート:');
    easymidi.getOutputs().forEach(port => console.log(`  - ${port}`));
    process.exit(1);
}

// 現在時刻を取得してフォーマット（HH:MM:SS）
function getCurrentTimeString() {
    const now = new Date();
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');
    const seconds = String(now.getSeconds()).padStart(2, '0');
    return `${hours}:${minutes}:${seconds}`;
}

// 文字列を6x11のビットマップ配列に変換
function stringToBitmap(timeString) {
    const bitmap = [];
    
    // 各文字のビットマップを横に連結
    for (let row = 0; row < 11; row++) {
        let bitmapRow = [];
        
        for (let charIndex = 0; charIndex < timeString.length; charIndex++) {
            const char = timeString[charIndex];
            const charBitmap = fontData[char];
            
            if (charBitmap) {
                // 6列分のデータを取得（row * 6からrow * 6 + 5まで）
                for (let col = 0; col < 6; col++) {
                    bitmapRow.push(charBitmap[row * 6 + col]);
                }
            } else {
                // 未定義の文字の場合は空白（6列分の0）
                for (let col = 0; col < 6; col++) {
                    bitmapRow.push(0);
                }
            }
        }
        
        bitmap.push(bitmapRow);
    }
    
    return bitmap;
}

// ビットマップをMIDIノートに変換して送信
function sendBitmapAsMidi(bitmap) {
    const baseNote = 36; // C2から開始
    const velocity = 100;
    
    // 全てのノートをオフにする
    for (let note = baseNote; note < baseNote + 128; note++) {
        output.send('noteoff', {
            note: note,
            velocity: 0,
            channel: 0
        });
    }
    
    // ビットマップの1の部分をノートオンで送信
    for (let row = 0; row < bitmap.length; row++) {
        for (let col = 0; col < bitmap[row].length; col++) {
            if (bitmap[row][col] === 1) {
                const note = baseNote + (row * 12) + (col % 12);
                if (note < 128) { // MIDIノート範囲内
                    output.send('noteon', {
                        note: note,
                        velocity: velocity,
                        channel: 0
                    });
                }
            }
        }
    }
}

// メイン処理
function updateTimeDisplay() {
    const timeString = getCurrentTimeString();
    console.log(`現在時刻: ${timeString}`);
    
    const bitmap = stringToBitmap(timeString);
    sendBitmapAsMidi(bitmap);
}

// 初回実行
updateTimeDisplay();

// 1秒ごとに更新
const interval = setInterval(updateTimeDisplay, 1000);

// プログラム終了時の処理
process.on('SIGINT', () => {
    console.log('\nプログラムを終了します...');
    clearInterval(interval);
    
    // 全てのノートをオフにする
    if (output) {
        for (let note = 36; note < 164; note++) {
            output.send('noteoff', {
                note: note,
                velocity: 0,
                channel: 0
            });
        }
        output.close();
    }
    
    process.exit(0);
});

console.log('現在時刻をMIDI出力に送信中... (Ctrl+Cで終了)');
